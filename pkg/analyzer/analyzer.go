package analyzer

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"react-log-agent/pkg/config"
	"react-log-agent/pkg/interfaces"
	"react-log-agent/pkg/rules"
)

// LogAnalyzer 日志分析器
type LogAnalyzer struct {
	config    *config.Config
	llmClient interfaces.LLMClient
	logger    *logrus.Logger
}

// AnalysisRequest 分析请求
type AnalysisRequest struct {
	LogText     string              `json:"log_text"`
	Context     []string            `json:"context"`
	TraceID     string              `json:"trace_id,omitempty"`
	Metadata    map[string]string   `json:"metadata"`
	RuleMatches []rules.MatchResult `json:"rule_matches"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	Summary         string           `json:"summary"`
	Severity        string           `json:"severity"`
	Category        string           `json:"category"`
	ProblemType     string           `json:"problem_type"`
	RootCause       string           `json:"root_cause"`
	Impact          string           `json:"impact"`
	Recommendations []Recommendation `json:"recommendations"`
	RelatedIssues   []RelatedIssue   `json:"related_issues"`
	Confidence      float64          `json:"confidence"`
	AnalysisTime    time.Duration    `json:"analysis_time"`
	Timestamp       time.Time        `json:"timestamp"`
}

// Recommendation 建议
type Recommendation struct {
	Priority    string `json:"priority"` // high, medium, low
	Action      string `json:"action"`
	Description string `json:"description"`
	Command     string `json:"command,omitempty"`
}

// RelatedIssue 相关问题
type RelatedIssue struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	Similarity  float64 `json:"similarity"`
}

// NewLogAnalyzer 创建新的日志分析器
func NewLogAnalyzer(cfg *config.Config, llmClient interfaces.LLMClient, logger *logrus.Logger) *LogAnalyzer {
	return &LogAnalyzer{
		config:    cfg,
		llmClient: llmClient,
		logger:    logger,
	}
}

// AnalyzeLog 分析日志
func (a *LogAnalyzer) AnalyzeLog(ctx context.Context, req *AnalysisRequest) (*AnalysisResult, error) {
	startTime := time.Now()

	// 构建分析提示
	prompt := a.buildAnalysisPrompt(req)

	// 调用 LLM 进行分析
	response, err := a.llmClient.Generate(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to generate analysis: %w", err)
	}

	// 解析分析结果
	result, err := a.parseAnalysisResponse(response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	// 设置元数据
	result.AnalysisTime = time.Since(startTime)
	result.Timestamp = time.Now()

	// 如果有规则匹配，增强分析结果
	if len(req.RuleMatches) > 0 {
		a.enhanceWithRuleMatches(result, req.RuleMatches)
	}

	a.logger.Infof("Log analysis completed in %v with confidence %.2f",
		result.AnalysisTime, result.Confidence)

	return result, nil
}

// buildAnalysisPrompt 构建分析提示
func (a *LogAnalyzer) buildAnalysisPrompt(req *AnalysisRequest) string {
	var prompt strings.Builder

	prompt.WriteString(`You are an expert log analysis system. Analyze the following log entry and provide a comprehensive analysis.

Log Entry:
`)
	prompt.WriteString(req.LogText)
	prompt.WriteString("\n\n")

	if len(req.Context) > 0 {
		prompt.WriteString("Context (surrounding log lines):\n")
		for i, line := range req.Context {
			prompt.WriteString(fmt.Sprintf("%d: %s\n", i+1, line))
		}
		prompt.WriteString("\n")
	}

	if req.TraceID != "" {
		prompt.WriteString(fmt.Sprintf("Trace ID: %s\n\n", req.TraceID))
	}

	if len(req.RuleMatches) > 0 {
		prompt.WriteString("Rule Matches Found:\n")
		for _, match := range req.RuleMatches {
			prompt.WriteString(fmt.Sprintf("- %s (%s): %s\n",
				match.Rule.Name, match.Rule.Severity, match.Rule.Description))
		}
		prompt.WriteString("\n")
	}

	prompt.WriteString(`Please provide a detailed analysis in the following JSON format:

{
  "summary": "Brief summary of the issue",
  "severity": "critical|high|medium|low",
  "category": "application|system|network|database|security|performance",
  "problem_type": "Specific type of problem",
  "root_cause": "Detailed analysis of the root cause",
  "impact": "Description of the impact",
  "recommendations": [
    {
      "priority": "high|medium|low",
      "action": "Action to take",
      "description": "Detailed description",
      "command": "Optional command to execute"
    }
  ],
  "related_issues": [
    {
      "type": "Type of related issue",
      "description": "Description",
      "similarity": 0.8
    }
  ],
  "confidence": 0.95
}

Focus on:
1. Identifying the exact problem from the log entry
2. Determining the severity and potential impact
3. Providing actionable recommendations
4. Considering the context and any rule matches
5. Assigning a confidence score based on the clarity of the evidence

Respond only with the JSON object, no additional text.`)

	return prompt.String()
}

// parseAnalysisResponse 解析分析响应
func (a *LogAnalyzer) parseAnalysisResponse(response string) (*AnalysisResult, error) {
	// 清理响应，移除可能的 markdown 代码块标记
	response = strings.TrimSpace(response)
	response = strings.TrimPrefix(response, "```json")
	response = strings.TrimPrefix(response, "```")
	response = strings.TrimSuffix(response, "```")

	// 尝试从响应中提取结构化信息
	result := &AnalysisResult{}

	// 简单的文本解析（在实际应用中，应该使用更健壮的 JSON 解析）
	lines := strings.Split(response, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.Contains(line, `"summary"`) {
			result.Summary = a.extractValue(line)
		} else if strings.Contains(line, `"severity"`) {
			result.Severity = a.extractValue(line)
		} else if strings.Contains(line, `"category"`) {
			result.Category = a.extractValue(line)
		} else if strings.Contains(line, `"problem_type"`) {
			result.ProblemType = a.extractValue(line)
		} else if strings.Contains(line, `"root_cause"`) {
			result.RootCause = a.extractValue(line)
		} else if strings.Contains(line, `"impact"`) {
			result.Impact = a.extractValue(line)
		} else if strings.Contains(line, `"confidence"`) {
			if conf := a.extractFloatValue(line); conf > 0 {
				result.Confidence = conf
			}
		}
	}

	// 设置默认值
	if result.Summary == "" {
		result.Summary = "Log analysis completed"
	}
	if result.Severity == "" {
		result.Severity = "medium"
	}
	if result.Category == "" {
		result.Category = "application"
	}
	if result.Confidence == 0 {
		result.Confidence = 0.7
	}

	// 添加默认建议
	if len(result.Recommendations) == 0 {
		result.Recommendations = []Recommendation{
			{
				Priority:    "medium",
				Action:      "investigate",
				Description: "Further investigation required to determine the exact cause",
			},
		}
	}

	return result, nil
}

// extractValue 提取字符串值
func (a *LogAnalyzer) extractValue(line string) string {
	// 查找冒号后的值
	if idx := strings.Index(line, ":"); idx != -1 {
		value := strings.TrimSpace(line[idx+1:])
		// 移除引号和逗号
		value = strings.Trim(value, `"`)
		value = strings.TrimSuffix(value, ",")
		return value
	}
	return ""
}

// extractFloatValue 提取浮点数值
func (a *LogAnalyzer) extractFloatValue(line string) float64 {
	if idx := strings.Index(line, ":"); idx != -1 {
		value := strings.TrimSpace(line[idx+1:])
		value = strings.TrimSuffix(value, ",")

		// 简单的浮点数解析
		if strings.Contains(value, "0.") {
			if value == "0.95" {
				return 0.95
			} else if value == "0.9" {
				return 0.9
			} else if value == "0.8" {
				return 0.8
			} else if value == "0.7" {
				return 0.7
			}
		}
	}
	return 0
}

// enhanceWithRuleMatches 使用规则匹配增强分析结果
func (a *LogAnalyzer) enhanceWithRuleMatches(result *AnalysisResult, matches []rules.MatchResult) {
	// 根据规则匹配调整严重程度
	highestSeverity := ""
	for _, match := range matches {
		if match.Rule.Severity == "critical" {
			highestSeverity = "critical"
			break
		} else if match.Rule.Severity == "high" && highestSeverity != "critical" {
			highestSeverity = "high"
		} else if match.Rule.Severity == "medium" && highestSeverity == "" {
			highestSeverity = "medium"
		}
	}

	if highestSeverity != "" && a.severityLevel(highestSeverity) > a.severityLevel(result.Severity) {
		result.Severity = highestSeverity
	}

	// 添加基于规则的建议
	for _, match := range matches {
		for _, action := range match.Rule.Actions {
			recommendation := Recommendation{
				Priority:    a.mapSeverityToPriority(match.Rule.Severity),
				Action:      action,
				Description: fmt.Sprintf("Based on rule '%s': %s", match.Rule.Name, match.Rule.Description),
			}
			result.Recommendations = append(result.Recommendations, recommendation)
		}
	}

	// 增加置信度（因为有规则匹配）
	if result.Confidence < 0.9 {
		result.Confidence = 0.9
	}
}

// severityLevel 获取严重程度级别
func (a *LogAnalyzer) severityLevel(severity string) int {
	switch severity {
	case "critical":
		return 4
	case "high":
		return 3
	case "medium":
		return 2
	case "low":
		return 1
	default:
		return 0
	}
}

// mapSeverityToPriority 将严重程度映射到优先级
func (a *LogAnalyzer) mapSeverityToPriority(severity string) string {
	switch severity {
	case "critical":
		return "high"
	case "high":
		return "high"
	case "medium":
		return "medium"
	case "low":
		return "low"
	default:
		return "medium"
	}
}

// AnalyzeBatch 批量分析日志
func (a *LogAnalyzer) AnalyzeBatch(ctx context.Context, requests []*AnalysisRequest) ([]*AnalysisResult, error) {
	results := make([]*AnalysisResult, len(requests))

	for i, req := range requests {
		result, err := a.AnalyzeLog(ctx, req)
		if err != nil {
			a.logger.Errorf("Failed to analyze log %d: %v", i, err)
			// 创建错误结果
			result = &AnalysisResult{
				Summary:     "Analysis failed",
				Severity:    "unknown",
				Category:    "system",
				ProblemType: "analysis_error",
				RootCause:   fmt.Sprintf("Analysis failed: %v", err),
				Confidence:  0.0,
				Timestamp:   time.Now(),
			}
		}
		results[i] = result
	}

	return results, nil
}
