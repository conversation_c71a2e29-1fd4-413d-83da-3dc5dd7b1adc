package agent

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"react-log-agent/pkg/config"
	"react-log-agent/pkg/interfaces"
)

// ReActAgent ReAct Agent 核心结构
type ReActAgent struct {
	config   *config.Config
	logger   *logrus.Logger
	tools    map[string]interfaces.Tool
	llm      interfaces.LLMClient
	maxSteps int
	verbose  bool
}

// Step ReAct 步骤
type Step struct {
	StepNumber  int       `json:"step_number"`
	Thought     string    `json:"thought"`
	Action      string    `json:"action"`
	ActionInput string    `json:"action_input"`
	Observation string    `json:"observation"`
	Timestamp   time.Time `json:"timestamp"`
}

// Result Agent 执行结果
type Result struct {
	Success    bool          `json:"success"`
	Answer     string        `json:"answer"`
	Steps      []Step        `json:"steps"`
	TotalSteps int           `json:"total_steps"`
	Duration   time.Duration `json:"duration"`
	Error      string        `json:"error,omitempty"`
}

// NewReActAgent 创建新的 ReAct Agent
func NewReActAgent(cfg *config.Config, llm interfaces.LLMClient, logger *logrus.Logger) *ReActAgent {
	agent := &ReActAgent{
		config:   cfg,
		logger:   logger,
		tools:    make(map[string]interfaces.Tool),
		llm:      llm,
		maxSteps: cfg.Agent.MaxSteps,
		verbose:  cfg.Agent.Verbose,
	}

	return agent
}

// RegisterTool 注册工具
func (a *ReActAgent) RegisterTool(tool interfaces.Tool) {
	a.tools[tool.Name()] = tool
	if a.verbose {
		a.logger.Infof("Registered tool: %s", tool.Name())
	}
}

// Run 运行 ReAct 循环
func (a *ReActAgent) Run(ctx context.Context, question string) (*Result, error) {
	startTime := time.Now()

	result := &Result{
		Steps: make([]Step, 0),
	}

	if a.verbose {
		a.logger.Infof("Starting ReAct agent for question: %s", question)
	}

	// 构建初始提示
	prompt := a.buildInitialPrompt(question)

	for stepNum := 1; stepNum <= a.maxSteps; stepNum++ {
		step := Step{
			StepNumber: stepNum,
			Timestamp:  time.Now(),
		}

		// 生成思考和行动
		response, err := a.llm.Generate(ctx, prompt)
		if err != nil {
			result.Error = fmt.Sprintf("LLM generation failed at step %d: %v", stepNum, err)
			result.Duration = time.Since(startTime)
			return result, err
		}

		// 解析响应
		thought, action, actionInput, finalAnswer, err := a.parseResponse(response)
		if err != nil {
			result.Error = fmt.Sprintf("Failed to parse response at step %d: %v", stepNum, err)
			result.Duration = time.Since(startTime)
			return result, err
		}

		step.Thought = thought
		step.Action = action
		step.ActionInput = actionInput

		if a.verbose {
			a.logger.Infof("Step %d - Thought: %s", stepNum, thought)
			a.logger.Infof("Step %d - Action: %s", stepNum, action)
		}

		// 如果是最终答案
		if action == "Final Answer" {
			result.Success = true
			result.Answer = finalAnswer
			result.Steps = append(result.Steps, step)
			result.TotalSteps = stepNum
			result.Duration = time.Since(startTime)

			if a.verbose {
				a.logger.Infof("Final answer reached at step %d: %s", stepNum, finalAnswer)
			}
			return result, nil
		}

		// 执行工具
		observation, err := a.executeTool(ctx, action, actionInput)
		if err != nil {
			observation = fmt.Sprintf("Error executing tool: %v", err)
		}

		step.Observation = observation
		result.Steps = append(result.Steps, step)

		if a.verbose {
			a.logger.Infof("Step %d - Observation: %s", stepNum, observation)
		}

		// 更新提示以包含新的步骤
		prompt = a.updatePrompt(prompt, step)
	}

	// 达到最大步数
	result.Error = fmt.Sprintf("Reached maximum steps (%d) without finding final answer", a.maxSteps)
	result.TotalSteps = a.maxSteps
	result.Duration = time.Since(startTime)

	return result, fmt.Errorf("max steps reached")
}

// buildInitialPrompt 构建初始提示
func (a *ReActAgent) buildInitialPrompt(question string) string {
	toolDescriptions := make([]string, 0, len(a.tools))
	for _, tool := range a.tools {
		toolDescriptions = append(toolDescriptions, fmt.Sprintf("- %s: %s", tool.Name(), tool.Description()))
	}

	prompt := fmt.Sprintf(`You are a log analysis expert. Your task is to analyze log-related questions and provide accurate answers.

You have access to the following tools:
%s

Use the following format:

Thought: I need to think about what to do
Action: the action to take, should be one of [%s]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: %s

Let's think step by step.

Thought:`,
		strings.Join(toolDescriptions, "\n"),
		strings.Join(a.getToolNames(), ", "),
		question)

	return prompt
}

// parseResponse 解析 LLM 响应
func (a *ReActAgent) parseResponse(response string) (thought, action, actionInput, finalAnswer string, err error) {
	lines := strings.Split(strings.TrimSpace(response), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "Thought:") {
			thought = strings.TrimSpace(strings.TrimPrefix(line, "Thought:"))
		} else if strings.HasPrefix(line, "Action:") {
			action = strings.TrimSpace(strings.TrimPrefix(line, "Action:"))
		} else if strings.HasPrefix(line, "Action Input:") {
			actionInput = strings.TrimSpace(strings.TrimPrefix(line, "Action Input:"))
		} else if strings.HasPrefix(line, "Final Answer:") {
			finalAnswer = strings.TrimSpace(strings.TrimPrefix(line, "Final Answer:"))
			action = "Final Answer"
			return
		}
	}

	if thought == "" {
		return "", "", "", "", fmt.Errorf("no thought found in response")
	}
	if action == "" {
		return "", "", "", "", fmt.Errorf("no action found in response")
	}

	return thought, action, actionInput, finalAnswer, nil
}

// executeTool 执行工具
func (a *ReActAgent) executeTool(ctx context.Context, toolName, input string) (string, error) {
	tool, exists := a.tools[toolName]
	if !exists {
		return "", fmt.Errorf("tool '%s' not found", toolName)
	}

	return tool.Execute(ctx, input)
}

// updatePrompt 更新提示
func (a *ReActAgent) updatePrompt(currentPrompt string, step Step) string {
	stepText := fmt.Sprintf(`
Thought: %s
Action: %s
Action Input: %s
Observation: %s

Thought:`, step.Thought, step.Action, step.ActionInput, step.Observation)

	return currentPrompt + stepText
}

// getToolNames 获取工具名称列表
func (a *ReActAgent) getToolNames() []string {
	names := make([]string, 0, len(a.tools))
	for name := range a.tools {
		names = append(names, name)
	}
	return names
}

// GetTools 获取已注册的工具
func (a *ReActAgent) GetTools() map[string]interfaces.Tool {
	return a.tools
}
